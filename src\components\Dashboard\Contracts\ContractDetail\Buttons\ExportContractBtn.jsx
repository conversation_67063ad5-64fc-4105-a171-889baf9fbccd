import { useExportContractMutation } from "@/redux/services/contractApiSlice";
import { showToast } from "@/util/showToast";
import { errorFinder } from "@/util/errorHandler";
import { useParams } from "react-router-dom";

function ExportContractBtn() {
    const { id } = useParams();
    const [exportContract, { isLoading }] = useExportContractMutation();

    const handleContractExport = async () => {
        try {
            await exportContract({ contract_id: id }).unwrap();
            showToast("Export uğurla tamamlandı.", "success");
        } catch (err) {
            console.error("Export error:", err);
            showToast(errorFinder(err), "error");
        }
    };

    return (
        <button
            onClick={handleContractExport}
            disabled={isLoading}
            className="defButton bgGreen mb-5"
        >
            {isLoading ? "Export Edilir..." : "Export Et"}
        </button>
    );
}

export default ExportContractBtn;
