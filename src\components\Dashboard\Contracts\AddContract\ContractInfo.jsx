import { useState, useEffect } from "react";
import classes from "./usercontract.module.css";
import { useGetUserProfileQuery } from "@/redux/services/userApiSlice";
import { calculateLoanData } from "@/util/loanCalculator";
import { CustomeTextField } from "./CustomTextField";
import { generateRows } from "@/util/generateContractInfoRows";

function ContractInfo() {
    const [formData, setFormData] = useState({
        product_name: "",
        product_quantity: 1,
        product_price: 0,
        loan_term: 12,
        initial_payment: 0,
        commission: 0,
        cost_amount: 0,
        monthly_payment: 0,
        investment: "",
        minMonthlyPayment: "",
        totalCost: "",
    });

    const [error, setError] = useState("");

    const { data: userProfile, isLoading: isUserInfoLoading } = useGetUserProfileQuery();

    function handleChange(e) {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    }

    useEffect(() => {
        try {
            setError("");
            const calculated = calculateLoanData({
                product_quantity: Number(formData.product_quantity),
                product_price: Number(formData.product_price),
                loan_term: Number(formData.loan_term),
                initial_payment: Number(formData.initial_payment),
                commission: Number(formData.commission),
                cost_amount: Number(formData.cost_amount),
                annualInterestRate: Number(userProfile?.company?.annual_interest_rate)
            });

            setFormData(prev => ({
                ...prev,
                investment: calculated.totalInvestment,
                minMonthlyPayment: calculated.minMonthlyPayment,
                totalCost: calculated.totalCost,
                monthly_payment: calculated.minMonthlyPayment,
            }));
        } catch (err) {
            setError(err.message);
            setFormData(prev => ({
                ...prev,
                investment: "",
                minMonthlyPayment: "",
                totalCost: "",
            }));
        }
    }, [
        formData.product_quantity,
        formData.product_price,
        formData.loan_term,
        formData.initial_payment,
        formData.commission,
        formData.cost_amount,
        userProfile?.company?.annual_interest_rate
    ]);

    useEffect(() => {
        const newTotalCost = Number(formData.monthly_payment) * Number(formData.loan_term);
        setFormData(prev => ({
            ...prev,
            totalCost: newTotalCost.toFixed(2),
        }));
    }, [formData.monthly_payment, formData.loan_term]);

    const rows = generateRows(formData, setFormData, userProfile);

    return (
        <div>
            <h2 className={classes.contractHeader}>Müqavilə məlumatları</h2>
            {error && <p className="text-red-500">{error}</p>}
            {isUserInfoLoading
                ? <p>Yüklənilir</p>
                : <div>
                    {rows.map((row, i) => (
                        <div key={i} className="flex items-center justify-between mt-[10px] gap-[40px]">
                            <span className={`w-1/2 ${row.bold ? 'font-bold' : 'text-[14px]'}`}>
                                {row.label}
                            </span>
                            <CustomeTextField
                                name={row.key}
                                value={row.value ?? formData[row.key]}
                                onChange={!row.disabled ? handleChange : undefined}
                                color={row.color}
                                disabled={row.disabled}
                                type={row.type}
                                onBlur={row.onBlur}
                                inputProps={row.inputProps}
                            />
                        </div>
                    ))}
                </div>

            }
        </div>
    );
}

export default ContractInfo;