import { useState, useEffect } from "react";

const PAGE_SIZE = 41; // Fetching a bit more to determine if there are more results

export function usePaginatedData(shouldFetch, searchQuery, additionalParams = {}, apiHook) {
  const [offset, setOffset] = useState(0);
  const [data, setData] = useState([]);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    setOffset(0);
    setData([]);
    setHasMore(false);
  }, [searchQuery]);

  const params = {
    limit: PAGE_SIZE,
    offset,
    ...additionalParams
  };

  // Add search query to params if provided
  if (searchQuery) {
    // For users, it's 'fullname', but this can be customized
    const searchField = additionalParams.searchField || 'fullname';
    params[searchField] = searchQuery;
  }

  // Handle case where no apiHook is provided
  const { data: apiData, isLoading, isFetching } = apiHook ? apiHook(params, {
    skip: !shouldFetch,
  }) : { data: null, isLoading: false, isFetching: false };

  useEffect(() => {
    if (apiData?.results && !isFetching) {
      setData(prev => {
        const newData = apiData.results;
        return offset === 0 ? newData : [...prev, ...newData];
      });
      setHasMore(apiData.results.length === PAGE_SIZE);
    }
  }, [apiData, offset, isFetching]);

  const loadMore = () => {
    if (!isFetching && hasMore) {
      setOffset(prev => prev + PAGE_SIZE);
    }
  };

  const reset = () => {
    setOffset(0);
    setData([]);
    setHasMore(false);
  };

  return {
    data,
    isLoading: isLoading || isFetching,
    isFetching,
    hasMore,
    loadMore,
    reset
  };
}
