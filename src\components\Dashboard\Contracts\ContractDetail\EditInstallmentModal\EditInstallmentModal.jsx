import { Dialog, DialogContent, TextField } from "@mui/material";
import { modalStyles } from "@/styles/shared/modalStyles";
import { useState, useEffect } from "react";
import { useUpdateInstallmentMutation } from "@/redux/services/contractApiSlice";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";

function EditInstallmentModal({ isOpen = false, onClose, contract }) {
  const [amount, setAmount] = useState("");
  const [updateInstallment, { isLoading }] = useUpdateInstallmentMutation();

  console.log(contract);

  useEffect(() => {
    if (contract && contract.paid_amount !== undefined) {
      setAmount(contract.paid_amount);
    }
  }, [contract]);

  async function handleSubmit(e) {
    e.preventDefault();

    if (!contract || !contract.id) {
      showToast("Ödəniş məlumatları tapılmadı", "error");
      return;
    }

    if (amount < 0) {
      showToast("Məbləğ 0-dan kiçik ola bilm<PERSON>", "error");
      return;
    }

    try {
      const data = {
        id: contract.id,
        data : {
          paid_amount: parseFloat(amount),
        }
      };

      console.log(data.data);

      await updateInstallment(data).unwrap();
      onClose();
    } catch (error) {
      console.log(error);
      const msg = errorFinder(error);
      showToast(msg, "error");
    }
  }

  return (
    <Dialog
      sx={modalStyles.installmentUpdate.sxstyle}
      slotProps={modalStyles.installmentUpdate.slotprops}
      open={isOpen}
      onClose={onClose}
    >
      <DialogContent>
        <form onSubmit={handleSubmit} className="w-full">
          <h2 className="modalTitle mb-[30px]">Ödəniş Düzəlişi</h2>
          <div className="flex w-full justify-between mb-[40px] items-center">
            <span className="w-1/3 text-[14px]">Məbləğ</span>
            <TextField
              name="amount"
              className="w-2/3"
              variant="standard"
              value={amount}
              type="number"
              onChange={(e) => setAmount(e.target.value)}
              inputProps={{ step: "any" }}
            />
          </div>
          <div className="flex gap-[28px] justify-end">
            <button type="button" onClick={onClose} className="defButton bgGray">Ləğv et</button>
            <button disabled={isLoading} className="defButton bgGreen">
              {isLoading ? "Yenilənir..." : "Yenilə"}
            </button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default EditInstallmentModal