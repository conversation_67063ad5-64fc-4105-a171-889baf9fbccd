import { usePayPayrollMutation } from "@/redux/services/userApiSlice";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";
import swal from "sweetalert"

function Buttons({ handleSelectedIds, selectedIds }) {
    const [payPayroll, { isLoading }] = usePayPayrollMutation();

    async function handlePayment() {
        if (selectedIds.length == 0) return;

        try {
            swal({
                title: "Seçilmiş əməkdaş(lar)ın əməkhaqqısını ödəməyə əminsinizmi?",
                icon: "warning",
                buttons: ["Xeyr", "Bəli"],
                dangerMode: true,
            }).then(async (willDelete) => {
                if (willDelete) {
                    await payPayroll({ payroll_ids: selectedIds }).unwrap();
                    showToast("Əməkhaqqı(lar) ödəndi. Məlumatlar yenilənilir...", "success");
                    handleSelectedIds("unSelectAll");
                }
            });
        } catch (error) {
            showToast(`Əməkhaqqı(lar) ödənilənəmədi. Xəta: ${errorFinder(error)}`, "error");
        }
    }

    return (
        <>
            <button disabled={isLoading || selectedIds.length == 0} onClick={handlePayment} className="defButton bgGreen">
                Ödə
            </button>
            <button onClick={() => handleSelectedIds("selectAll")} className="defButton bgGreen">
                Hamısını seç
            </button>
            <button onClick={() => handleSelectedIds("unSelectAll")} className="defButton bgGreen">
                Seçilmişləri sil
            </button>
        </>
    )
}

export default Buttons