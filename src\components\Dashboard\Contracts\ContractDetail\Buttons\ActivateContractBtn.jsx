import { useParams } from "react-router-dom";

import { useUpdateContractStatusMutation } from "@/redux/services/contractApiSlice";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";

function ActivateContractBtn({ contractDetail }) {
    const { id } = useParams();

    const [updateStatus, { isLoading: updateContractLoading }] = useUpdateContractStatusMutation();
    async function handleUpdateStatus() {
        try {
            if (contractDetail.status != "WAITING") {
                showToast("Aktiv etmə yalnız müqavilə statusu Gözləmə olan müqavilələr üçündür", "error");
                return;
            }
            const data = {
                id: id,
                updateStatusData: {
                    id: id,
                    status: "ONGOING"
                }
            }

            await updateStatus(data).unwrap();
        } catch (error) {
            const msg = errorFinder(error);
            showToast(msg, "error");
        }
    }

    return (
        <>
            {contractDetail.status == "WAITING"
                && <button
                    onClick={handleUpdateStatus}
                    disabled={updateContractLoading || contractDetail.status != "WAITING"}
                    className="defButton bgGreen mb-5 "
                >
                    Aktiv et
                </button>
            }
        </>
    )
}

export default ActivateContractBtn