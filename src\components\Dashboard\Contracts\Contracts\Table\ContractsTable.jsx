import generalTableStyles from "@/styles/shared/generalTableStyles.module.css"
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import TableHead from "./TableHead";
import TableData from "./TableData";
import { useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { useGetAllContractsQuery } from "@/redux/services/contractApiSlice";
import { roundTo2 } from "@/util/formatters";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";

function ContractsTable() {
    const [searchParams] = useSearchParams();

    const limit = parseInt(searchParams.get("limit") || "16", 10);
    const offset = parseInt(searchParams.get("offset") || "0", 10);

    const queryObject = {
        ...Object.fromEntries(searchParams.entries()),
        limit,
        offset,
    };

    const { data: contracts, isLoading, isError, error } = useGetAllContractsQuery(queryObject);

    const { isSearchOpen } = useSelector(state => state.layout);

    if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

    return (
        <div className="pb-8 pt-[24px] w-full">
            <div className={`${generalTableStyles.tableWrapper} ${isSearchOpen && generalTableStyles.searchOpened}`}>
                {isLoading ? (
                    <ContentLoadingScreen />
                ) : (
                    contracts
                        && contracts.results
                        && contracts.results.data
                        && contracts.results.data.length > 0 ?
                        <table className={generalTableStyles.table}>
                            <TableHead />
                            <tbody className={generalTableStyles.tbody}>
                                {contracts.results.data.map(contract => <TableData key={contract.id} contract={contract} />)}
                            </tbody>
                            <tfoot className={generalTableStyles.tfoot}>
                                <tr>
                                    <td>Sıralanır: {contracts.count}</td>
                                    <td colSpan={4}></td>
                                    <td className="whitespace-nowrap text-white">{roundTo2(contracts.results.total_remaining_debt)}</td>
                                    <td colSpan={2}></td>
                                </tr>
                            </tfoot>
                        </table>
                        : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
                )}
            </div>
            {contracts
                && !isLoading
                && <PaginationComponent totalCount={contracts.count} />}
        </div>
    )
}

export default ContractsTable
