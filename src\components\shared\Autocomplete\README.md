# AutocompleteWrapper - Reusable Component

The `AutocompleteWrapper` component has been made reusable to work with different data types, not just users. It maintains backward compatibility while allowing for new configurations.

## Usage

### Default Usage (Users - Backward Compatible)
```jsx
import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper";

// This works exactly as before
<AutocompleteWrapper 
    type="customer" 
    label="Mü<PERSON>təri *" 
/>
```

### Custom Data Configuration
```jsx
import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper";
import { useGetAllPositionsQuery, useGetSinglePositionQuery } from "@/redux/services/userApiSlice";

// Example: Using with positions
const positionConfig = {
    apiHook: useGetAllPositionsQuery,
    searchField: "name", // Field to search by
    displayField: "name", // Field to display in options
    idField: "id", // Field to use as unique identifier
    loadingText: "Yüklənilir…", // Loading text
    singleItemApiHook: useGetSinglePositionQuery, // Optional: for single item fetching
    singleItemParamName: "positionId", // Optional: param name for single item
    filterLogic: null // Optional: custom filter function
};

<AutocompleteWrapper 
    type="position" 
    label="Vəzifə seç *"
    dataConfig={positionConfig}
/>
```

### Example with Cities
```jsx
import { useGetAllCitiesQuery } from "@/redux/services/userApiSlice";

const cityConfig = {
    apiHook: useGetAllCitiesQuery,
    searchField: "name",
    displayField: "name",
    idField: "id",
    loadingText: "Yüklənilir…"
};

<AutocompleteWrapper 
    type="city" 
    label="Şəhər seç *"
    dataConfig={cityConfig}
/>
```

### Example with Custom Filter Logic
```jsx
const customFilterConfig = {
    apiHook: useGetAllUsersQuery,
    searchField: "fullname",
    displayField: "fullname",
    idField: "id",
    loadingText: "Yüklənilir…",
    filterLogic: (items, autocompleteData, type) => {
        // Custom filtering logic
        return items.filter(item => {
            // Your custom filter conditions
            return item.is_active === true;
        });
    }
};

<AutocompleteWrapper 
    type="activeUsers" 
    label="Aktiv istifadəçilər *"
    dataConfig={customFilterConfig}
/>
```

## Configuration Options

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `apiHook` | Function | `useGetAllUsersQuery` | RTK Query hook for fetching data |
| `searchField` | String | `"fullname"` | Field name to search by |
| `displayField` | String | `"fullname"` | Field name to display in options |
| `idField` | String | `"id"` | Field name to use as unique identifier |
| `loadingText` | String | `"Yüklənilir…"` | Text to show while loading |
| `singleItemApiHook` | Function | `useGetSingleUsersQuery` | Optional: Hook for fetching single item |
| `singleItemParamName` | String | `"customerParamId"` | Optional: Parameter name for single item |
| `filterLogic` | Function | `null` | Optional: Custom filter function |

## Redux State Management

The component uses the `infiniteScrollSlice` to manage state. You need to add your new type to the initial state:

```javascript
// In src/redux/features/infiniteScrollSlice.js
const initialState = {
  customer: { ...userTemplate },
  guarantor: { ...userTemplate },
  attorney: { ...userTemplate },
  executor: { ...userTemplate },
  investor: { ...userTemplate },
  expense: { ...userTemplate },
  administrator: { ...userTemplate },
  // Add your new types here
  position: { ...userTemplate },
  city: { ...userTemplate },
  activeUsers: { ...userTemplate },
};
```

## API Requirements

Your API hook should:
1. Accept parameters including `limit`, `offset`, and your search field
2. Return data in the format: `{ results: [...], count: number }`
3. Support pagination with `limit` and `offset` parameters

## Backward Compatibility

All existing usage of AutocompleteWrapper will continue to work without any changes. The component automatically detects when no `dataConfig` is provided and uses the default user configuration.
