# AutocompleteWrapper - Fully Reusable Component

The `AutocompleteWrapper` component has been completely refactored to be fully reusable for any data type. User-specific logic has been moved to `UserAutocompleteWrapper`.

## Components

### AutocompleteWrapper (Generic)
A fully generic autocomplete component that requires a `dataConfig` prop.

### UserAutocompleteWrapper (User-Specific)
A wrapper around AutocompleteWrapper that handles user-specific logic and maintains backward compatibility.

## Usage

### User Data (Backward Compatible)
```jsx
import UserAutocompleteWrapper from "@/components/shared/Autocomplete/UserAutocompleteWrapper";

// This works exactly as before
<UserAutocompleteWrapper 
    type="customer" 
    label="Müştəri *" 
/>
```

### Generic Data (New)
```jsx
import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper";
import { useGetAllPositionsQuery, useGetSinglePositionQuery } from "@/redux/services/userApiSlice";

// Example: Using with positions
const positionConfig = {
    apiHook: useGetAllPositionsQuery,
    searchField: "name", // Field to search by
    displayField: "name", // Field to display in options
    idField: "id", // Field to use as unique identifier
    loadingText: "Yüklənilir…", // Loading text
    singleItemApiHook: useGetSinglePositionQuery, // Optional: for single item fetching
    singleItemParam: positionId, // Optional: param for single item
    filterLogic: null // Optional: custom filter function
};

<AutocompleteWrapper 
    type="position" 
    label="Vəzifə seç *"
    dataConfig={positionConfig}
/>
```

### Example with Custom Filter Logic
```jsx
const customFilterConfig = {
    apiHook: useGetAllUsersQuery,
    searchField: "fullname",
    displayField: "fullname",
    idField: "id",
    loadingText: "Yüklənilir…",
    filterLogic: (items, autocompleteData, type) => {
        // Custom filtering logic
        return items.filter(item => item.is_active === true);
    }
};

<AutocompleteWrapper 
    type="activeUsers" 
    label="Aktiv istifadəçilər *"
    dataConfig={customFilterConfig}
/>
```

## Configuration Options

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `apiHook` | Function | Yes | RTK Query hook for fetching data |
| `searchField` | String | Yes | Field name to search by |
| `displayField` | String | Yes | Field name to display in options |
| `idField` | String | Yes | Field name to use as unique identifier |
| `loadingText` | String | Yes | Text to show while loading |
| `singleItemApiHook` | Function | No | Hook for fetching single item |
| `singleItemParam` | Any | No | Parameter for single item fetching |
| `filterLogic` | Function | No | Custom filter function |

## Redux State Management

Add your new type to the initial state in `infiniteScrollSlice.js`:

```javascript
const initialState = {
  // Existing user types
  customer: { ...dataTemplate },
  guarantor: { ...dataTemplate },
  // Add your new types here
  position: { ...dataTemplate },
  city: { ...dataTemplate },
};
```

## Migration Guide

### Before (Old Usage)
```jsx
import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper";

<AutocompleteWrapper type="customer" label="Müştəri *" />
```

### After (New Usage)
```jsx
import UserAutocompleteWrapper from "@/components/shared/Autocomplete/UserAutocompleteWrapper";

<UserAutocompleteWrapper type="customer" label="Müştəri *" />
```

All existing user-related autocomplete usage should be updated to use `UserAutocompleteWrapper` instead of `AutocompleteWrapper`.

## API Requirements

Your API hook should:
1. Accept parameters including `limit`, `offset`, and your search field
2. Return data in the format: `{ results: [...], count: number }`
3. Support pagination with `limit` and `offset` parameters
