import { TextField } from "@mui/material"

import classes from "./usercontract.module.css"
import { useSelector } from "react-redux"
import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper"

function CustomerInfo() {
    const { attorney, guarantor } = useSelector(state => state.infiniteScroll);

    const date = new Date().toISOString().split("T")[0];

    return (
        <div className=" my-div-f">
            <style>{`.my-div-f {
                        width: auto;
                        min-width: 350px;}

                        @media (max-width: 991px) {
                            .my-div {
                            min-width: unset;
                            }
                        }
            `}</style>
            <h2 className={classes.contractHeader}><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> məlumatları</h2>
            <div>
                <AutocompleteWrapper type="customer" label="Müştəri *" />
                {/* <div className="flex items-end justify-between my-[14px]">
                    <span className="w-1/2 text-[14px]">M<PERSON>dar<PERSON></span>
                    <div className="flex items-end gap-2 w-1/2">
                        <Autocomplete
                            size="small"
                            clearOnEscape
                            className="w-3/5"
                            options={azerbaijanCities}
                            renderInput={(params) => (
                                <TextField {...params} variant="standard" />
                            )}
                        />
                        <TextField className="w-2/5" variant="standard" />
                    </div>
                </div> */}
                <AutocompleteWrapper type="guarantor" label="Kəfil" isOptionalField={true}>
                    <div className="flex items-center justify-between my-[14px]">
                        <span className="w-1/3 text-[14px]">Kəfil tel:</span>
                        <TextField className="w-2/3" variant="standard" disabled value={guarantor.selectedValue?.phone1 || ""} />
                    </div>
                </AutocompleteWrapper>
                <AutocompleteWrapper type="attorney" label="Vəkil" isOptionalField={true}>
                    <div className="flex items-center justify-between my-[14px]">
                        <span className="w-1/3 text-[14px]">Vəkil tel:</span>
                        <TextField className="w-2/3" variant="standard" disabled value={attorney.selectedValue?.phone1 || ""} />
                    </div>
                </AutocompleteWrapper>
                {/* <AttorneyAutocomplete /> */}
            </div>
            <div className="flex justify-between">
                <label htmlFor="contract_date">Tarix *</label>
                <input defaultValue={date} className="w-2/3 border-b-2 border-black outline-none text-[14px]" type="date" name="contract_date" id="contract_date" required />
            </div>
            <div className="mt-10">
                <TextField
                    id="outlined-multiline-static"
                    name="note"
                    label="Qeyd"
                    multiline
                    minRows={4}
                    className="w-full"
                    variant="outlined"
                />
            </div>
        </div>
    )
}

export default CustomerInfo