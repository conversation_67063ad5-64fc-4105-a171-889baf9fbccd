import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useGetSingleUsersQuery, useGetAllUsersQuery } from "@/redux/services/userApiSlice";
import { setData } from "@/redux/features/infiniteScrollSlice";
import AutocompleteWrapper from "./AutocompleteWrapper";

/**
 * User-specific wrapper around AutocompleteWrapper that handles user-related logic
 * This component maintains backward compatibility with existing user autocomplete usage
 */
function UserAutocompleteWrapper({
    type,
    label,
    children,
    queryParams = {},
    defaultValue = {},
    isOptionalField = false,
    isLabelAvailable = true
}) {
    const { customerParamId } = useParams();
    const dispatch = useDispatch();
    const { searchQuery, fullname } = useSelector(state => state.infiniteScroll[type]);

    // Sync fullname and searchQuery for backward compatibility
    useEffect(() => {
        if (searchQuery !== fullname) {
            dispatch(setData({ mainKey: type, key: "fullname", value: searchQuery }));
        }
    }, [searchQuery, fullname, dispatch, type]);

    // User-specific filter logic for customer/guarantor/attorney relationships
    const userFilterLogic = (items, autocompleteData, currentType) => {
        const customerId = Number(autocompleteData.customer?.selectedValue?.id);
        const attorneyId = autocompleteData.attorney?.selectedValue?.id;
        const guarantorId = autocompleteData.guarantor?.selectedValue?.id;
        
        if ((currentType === "guarantor" || currentType === "attorney") && customerId) {
            return items.filter(u => u.id !== customerId);
        } else if (currentType === "customer" && (attorneyId || guarantorId)) {
            return items.filter(u => {
                if (attorneyId && u.id === attorneyId) return false;
                if (guarantorId && u.id === guarantorId) return false;
                return true;
            });
        }
        
        return items;
    };

    // Configuration for user data
    const userDataConfig = {
        apiHook: useGetAllUsersQuery,
        searchField: "fullname",
        displayField: "fullname",
        idField: "id",
        loadingText: "Yüklənilir…",
        singleItemApiHook: type === "customer" ? useGetSingleUsersQuery : null,
        singleItemParam: type === "customer" ? customerParamId : null,
        filterLogic: userFilterLogic
    };

    return (
        <AutocompleteWrapper
            type={type}
            label={label}
            queryParams={queryParams}
            defaultValue={defaultValue}
            isOptionalField={isOptionalField}
            isLabelAvailable={isLabelAvailable}
            dataConfig={userDataConfig}
        >
            {children}
        </AutocompleteWrapper>
    );
}

export default UserAutocompleteWrapper;
