import { useParams } from "react-router-dom";
import { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useGetSingleUsersQuery, useGetAllUsersQuery } from "@/redux/services/userApiSlice";
import { setData } from "@/redux/features/infiniteScrollSlice";
import AutocompleteWrapper from "./AutocompleteWrapper";

/**
 * User-specific wrapper around AutocompleteWrapper that handles user-related logic
 * This component maintains backward compatibility with existing user autocomplete usage
 */
function UserAutocompleteWrapper({
    type,
    label,
    children,
    queryParams = {},
    defaultValue = {},
    isOptionalField = false,
    isLabelAvailable = true
}) {
    const { customerParamId } = useParams();
    const dispatch = useDispatch();
    const didInit = useRef(false);
    const { selectedValue } = useSelector(state => state.infiniteScroll[type]);

    // Handle default value initialization (moved from AutocompleteWrapper)
    useEffect(() => {
        if (
            !didInit.current &&
            defaultValue?.id != null &&
            !selectedValue
        ) {
            dispatch(setData({
                mainKey: type,
                key: "selectedValue",
                value: defaultValue
            }));
            didInit.current = true;
        }
    }, [defaultValue, selectedValue, type, dispatch]);

    // Single item fetching for customer type (moved from AutocompleteWrapper)
    const { data: singleItem } = useGetSingleUsersQuery(customerParamId, {
        skip: !customerParamId || type !== "customer"
    });

    useEffect(() => {
        if (type === "customer" && singleItem && customerParamId) {
            dispatch(setData({
                mainKey: type,
                key: "selectedValue",
                value: {
                    fullname: singleItem.fullname,
                    id: customerParamId
                }
            }));
        }
    }, [singleItem, customerParamId, type, dispatch]);

    // User-specific filter logic for customer/guarantor/attorney relationships
    const userFilterLogic = (items, autocompleteData, currentType) => {
        const customerId = Number(autocompleteData.customer?.selectedValue?.id);
        const attorneyId = autocompleteData.attorney?.selectedValue?.id;
        const guarantorId = autocompleteData.guarantor?.selectedValue?.id;
        
        if ((currentType === "guarantor" || currentType === "attorney") && customerId) {
            return items.filter(u => u.id !== customerId);
        } else if (currentType === "customer" && (attorneyId || guarantorId)) {
            return items.filter(u => {
                if (attorneyId && u.id === attorneyId) return false;
                if (guarantorId && u.id === guarantorId) return false;
                return true;
            });
        }
        
        return items;
    };

    // Configuration for user data
    const userDataConfig = {
        apiHook: useGetAllUsersQuery,
        searchField: "fullname",
        displayField: "fullname",
        idField: "id",
        loadingText: "Yüklənilir…",
        filterLogic: userFilterLogic
    };

    return (
        <AutocompleteWrapper
            type={type}
            label={label}
            queryParams={queryParams}
            defaultValue={defaultValue}
            isOptionalField={isOptionalField}
            isLabelAvailable={isLabelAvailable}
            dataConfig={userDataConfig}
        >
            {children}
        </AutocompleteWrapper>
    );
}

export default UserAutocompleteWrapper;
