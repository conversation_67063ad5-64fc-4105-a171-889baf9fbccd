import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper";
import UserAutocompleteWrapper from "@/components/shared/Autocomplete/UserAutocompleteWrapper";
import { useGetAllPositionsQuery, useGetSinglePositionQuery, useGetAllUsersQuery } from "@/redux/services/userApiSlice";

/**
 * Example component demonstrating different ways to use the reusable AutocompleteWrapper
 */
function AutocompleteExamples() {
    // Configuration for positions autocomplete
    const positionConfig = {
        apiHook: useGetAllPositionsQuery,
        searchField: "name",
        displayField: "name", 
        idField: "id",
        loadingText: "Vəzifələr yüklənilir…",
        singleItemApiHook: useGetSinglePositionQuery,
        singleItemParamName: "positionId"
    };

    // Configuration for active users only
    const activeUsersConfig = {
        apiHook: useGetAllUsersQuery,
        searchField: "fullname",
        displayField: "fullname",
        idField: "id", 
        loadingText: "Aktiv istifadəçilər yüklənilir…",
        filterLogic: (items, autocompleteData, type) => {
            // Filter to show only active users
            return items.filter(user => user.is_active === true);
        }
    };

    // Configuration for employees only
    const employeeConfig = {
        apiHook: useGetAllUsersQuery,
        searchField: "fullname",
        displayField: "fullname",
        idField: "id",
        loadingText: "İşçilər yüklənilir…",
        filterLogic: (items, autocompleteData, type) => {
            // Filter to show only employees
            return items.filter(user => user.is_employee === true);
        }
    };

    return (
        <div className="p-4 space-y-6">
            <h2 className="text-xl font-bold">AutocompleteWrapper Examples</h2>
            
            {/* Default usage - backward compatible */}
            <div>
                <h3 className="text-lg font-semibold mb-2">1. Default Usage (Users)</h3>
                <UserAutocompleteWrapper
                    type="customer"
                    label="Müştəri seç *"
                />
            </div>

            {/* Positions example */}
            <div>
                <h3 className="text-lg font-semibold mb-2">2. Positions Autocomplete</h3>
                <AutocompleteWrapper 
                    type="position" 
                    label="Vəzifə seç *"
                    dataConfig={positionConfig}
                />
            </div>

            {/* Active users only */}
            <div>
                <h3 className="text-lg font-semibold mb-2">3. Active Users Only</h3>
                <AutocompleteWrapper 
                    type="activeUsers" 
                    label="Aktiv istifadəçi seç *"
                    dataConfig={activeUsersConfig}
                />
            </div>

            {/* Employees only */}
            <div>
                <h3 className="text-lg font-semibold mb-2">4. Employees Only</h3>
                <AutocompleteWrapper 
                    type="employee" 
                    label="İşçi seç *"
                    dataConfig={employeeConfig}
                />
            </div>

            {/* Optional field example */}
            <div>
                <h3 className="text-lg font-semibold mb-2">5. Optional Field</h3>
                <UserAutocompleteWrapper
                    type="guarantor"
                    label="Zamin (İstəyə bağlı)"
                    isOptionalField={true}
                />
            </div>

            {/* Without label */}
            <div>
                <h3 className="text-lg font-semibold mb-2">6. Without Label</h3>
                <UserAutocompleteWrapper
                    type="attorney"
                    label="Vəkil"
                    isLabelAvailable={false}
                />
            </div>
        </div>
    );
}

export default AutocompleteExamples;
