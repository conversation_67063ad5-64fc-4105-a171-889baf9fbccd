export const operationTypes = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: "" },
  { name: "<PERSON>n<PERSON><PERSON><PERSON>", value: "INVESTMENT" },
  { name: "<PERSON><PERSON><PERSON>", value: "CREDIT_PAYMENT" },
  { name: "<PERSON><PERSON><PERSON>", value: "WITHDRAW" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: "SALE" },
  { name: "Transfer", value: "TRANSFER" },
];

export const paymentStatusTypes = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: "" },
  { name: "<PERSON>dən<PERSON><PERSON>", value: "PAID" },
  { name: "<PERSON>dən<PERSON><PERSON><PERSON>ə<PERSON>", value: "UNPAID" },
  { name: "<PERSON><PERSON><PERSON>", value: "INCOMPLETE" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", value: "DELAYED" },
  { name: "<PERSON><PERSON><PERSON>", value: "OVERPAYMENT" },
];

export const contractStatusTypes = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: "" },
  { name: "<PERSON>ə<PERSON>", value: "COMPLETED" },
  { name: "<PERSON><PERSON><PERSON> edən", value: "ONGOING" },
  { name: "Ləğv edilən", value: "CANCELLED" },
  { name: "<PERSON><PERSON><PERSON><PERSON>əmə", value: "WAITING" },
];

export const payrollStatusTypes = [
  { name: "Hamısı", value: "" },
  { name: "Ödənən", value: "PAID" },
  { name: "Ödənməyən", value: "UNPAID" },
];

export const contactColorsData = {
  white: "overdue",
  green: "completepayment",
  magenta: "allPaidContracts",
  red: "latepayment",
  yellow: "incompletepayment",
  gray: "allPaidInstallments",
  "dark green": "overPayment",
};

export const DRAWER_WIDTHS = {
  COLLAPSED_WIDTH: 75,
  EXPANDED_WIDTH: 315,
};

export const MONTH_NAMES = [
  "Yan",
  "Fev",
  "Mar",
  "Apr",
  "May",
  "İyn",
  "İyl",
  "Avq",
  "Sen",
  "Okt",
  "Noy",
  "Dek",
];

export const WEEK_DAY_NAMES = [
  "Baz",
  "B.e.",
  "Ç.a.",
  "Çər.",
  "C.a.",
  "Cümə",
  "Şən.",
];
