import { Autocomplete, CircularProgress, TextField } from "@mui/material";
import { usePaginatedData } from "@/hooks/usePaginatedData";
import { usePaginatedUsers } from "@/hooks/usePaginatedUsers";
import { useDispatch, useSelector } from "react-redux";
import { setData } from "@/redux/features/infiniteScrollSlice";
import { useParams } from "react-router-dom";
import { useEffect, useRef, useCallback } from "react";
import { useGetSingleUsersQuery, useGetAllUsersQuery } from "@/redux/services/userApiSlice";

function AutocompleteWrapper({
    type,
    label,
    children,
    queryParams = {},
    defaultValue = {},
    isOptionalField = false,
    isLabelAvailable = true,
    // New props for reusability
    dataConfig = null
}) {
    const { customerParamId } = useParams();
    const dispatch = useDispatch();
    const observerRef = useRef(null);
    const hasInteracted = useRef(false);
    const didInit = useRef(false);

    // Default configuration for backward compatibility (users)
    const defaultConfig = {
        apiHook: useGetAllUsersQuery,
        searchField: "fullname",
        displayField: "fullname",
        idField: "id",
        loadingText: "Yüklənilir…",
        singleItemApiHook: useGetSingleUsersQuery,
        singleItemParamName: "customerParamId",
        filterLogic: null
    };

    // Use provided config or default to users
    const config = dataConfig || defaultConfig;
    const isUserType = !dataConfig; // Backward compatibility flag

    const { fullname, isListOpen, selectedValue, error } =
        useSelector(state => state.infiniteScroll[type]);
    const autocompleteData = useSelector(state => state.infiniteScroll);

    useEffect(() => {
        if (
            !didInit.current &&
            defaultValue?.[config.idField] != null &&
            type !== "customer" &&
            !selectedValue
        ) {
            dispatch(setData({
                mainKey: type,
                key: "selectedValue",
                value: defaultValue
            }));
            didInit.current = true;
        }
    }, [defaultValue, selectedValue, type, dispatch, config.idField]);

    // Single item fetching (for backward compatibility with users)
    const { data: singleItem } = config.singleItemApiHook ?
        config.singleItemApiHook(customerParamId, {
            skip: !customerParamId || type !== "customer"
        }) : { data: null };

    useEffect(() => {
        if (type === "customer" && singleItem && customerParamId && config.singleItemApiHook) {
            dispatch(setData({
                mainKey: "customer",
                key: "selectedValue",
                value: {
                    [config.displayField]: singleItem[config.displayField],
                    [config.idField]: customerParamId
                }
            }));
        }
    }, [singleItem, customerParamId, type, dispatch, config.displayField, config.idField, config.singleItemApiHook]);

    // Always call both hooks but use skip parameter to control which one actually fetches
    const userResult = usePaginatedUsers(isUserType && isListOpen, fullname, queryParams);
    const genericResult = usePaginatedData(!isUserType && isListOpen, fullname, { ...queryParams, searchField: config.searchField }, config.apiHook);

    // Use the appropriate result based on configuration
    const { data, users, isLoading, isFetching, hasMore, loadMore, reset } = isUserType ?
        { ...userResult, data: userResult.users } :
        { ...genericResult, users: genericResult.data };

    const items = users || data;

    const lastOptionRef = useCallback(
        node => {
            if (isLoading || isFetching) return;
            if (observerRef.current) observerRef.current.disconnect();
            observerRef.current = new IntersectionObserver(entries => {
                if (entries[0].isIntersecting && hasMore) {
                    loadMore();
                }
            });
            if (node) observerRef.current.observe(node);
        },
        [isLoading, isFetching, hasMore, loadMore]
    );

    // Apply filtering logic
    let filteredItems = items;

    if (config.filterLogic) {
        // Use custom filter logic if provided
        filteredItems = config.filterLogic(items, autocompleteData, type);
    } else if (isUserType) {
        // Default user filtering logic for backward compatibility
        const customerId = Number(autocompleteData.customer?.selectedValue?.id);
        const attorneyId = autocompleteData.attorney?.selectedValue?.id;
        const guarantorId = autocompleteData.guarantor?.selectedValue?.id;

        if ((type === "guarantor" || type === "attorney") && customerId) {
            filteredItems = items.filter(u => u[config.idField] !== customerId);
        } else if (type === "customer" && (attorneyId || guarantorId)) {
            filteredItems = items.filter(u => {
                if (attorneyId && u[config.idField] === attorneyId) return false;
                if (guarantorId && u[config.idField] === guarantorId) return false;
                return true;
            });
        }
    }

    return (
        <>
            <div
                className={`flex ${error && !isOptionalField ? "items-center" : "items-end"
                    } justify-between my-[14px]`}
            >
                {isLabelAvailable &&
                    <span className="w-1/3 text-[14px]">{label}</span>
                }
                <Autocomplete
                    id={`${isUserType ? 'userSelector' : 'dataSelector'}-${type}`}
                    size="small"
                    className={`${!isLabelAvailable ? "w-full" : "w-2/3"}`}
                    onOpen={() =>
                        dispatch(
                            setData({ mainKey: type, key: "isListOpen", value: true })
                        )
                    }
                    onClose={() => {
                        dispatch(
                            setData({ mainKey: type, key: "isListOpen", value: false })
                        );
                        reset();
                    }}
                    options={filteredItems}
                    getOptionLabel={option => option[config.displayField] || ""}
                    loading={isLoading}
                    isOptionEqualToValue={(option, value) => option[config.idField] === value?.[config.idField]}
                    onChange={(_, newVal) => {
                        hasInteracted.current = true;
                        dispatch(
                            setData({ mainKey: type, key: "selectedValue", value: newVal })
                        );
                        dispatch(setData({ mainKey: type, key: "error", value: false }));
                    }}
                    value={selectedValue}
                    onInputChange={(_, newInputValue) =>
                        dispatch(
                            setData({ mainKey: type, key: "fullname", value: newInputValue })
                        )
                    }
                    renderOption={(props, option, state) => {
                        const isLast = state.index === filteredItems.length - 1;
                        return (
                            <li
                                {...props}
                                key={option[config.idField]}
                                ref={isLast ? lastOptionRef : undefined}
                            >
                                {option[config.displayField]}
                                {isLast && (isLoading || isFetching) && <em> {config.loadingText}</em>}
                            </li>
                        );
                    }}
                    renderInput={params => (
                        <TextField
                            {...params}
                            variant="standard"
                            error={!isOptionalField && error}
                            helperText={
                                !isOptionalField && error ? "Bu hissə boş ola bilməz" : ""
                            }
                            InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                    <>
                                        {(isLoading || isFetching) && (
                                            <CircularProgress size={20} />
                                        )}
                                        {params.InputProps.endAdornment}
                                    </>
                                )
                            }}
                        />
                    )}
                />
            </div>
            {children}
        </>
    );
}

export default AutocompleteWrapper;