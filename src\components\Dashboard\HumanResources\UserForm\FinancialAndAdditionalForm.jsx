import { FormControl, MenuItem, Select, TextField } from "@mui/material"

import classes from "./userform.module.css";
import { useNavigate, useParams } from "react-router-dom";
import { useFormContext } from "react-hook-form";

function FinancialAndAdditionalForm({ user = {}, userFormConfig, userProfile }) {
    const { register, formState: { errors } } = useFormContext();

    const { id } = useParams();
    const navigate = useNavigate();

    function handleContractClick(filterVal) {
        navigate(`/contracts?limit=13&offset=0&${filterVal}=${id}`)
    }

    const canSeePortmanat = userFormConfig?.[userProfile.role].canAddOrSeePortmanat;

    return (
        <div className="max-w-[427px] w-full">
            {id
                &&
                <>
                    <div className="flex justify-between my-[27px]">
                        <label className="text-[14px] font-[500]" htmlFor="userContracts">Müqavilə</label>
                        <button
                            type="button"
                            onClick={user.customer_contract_count > 0 ? () => handleContractClick("customer") : null}
                            className={`w-1/2 text-start ${classes.userAddInput} ${user.customer_contract_count == 0 && "!cursor-default"}`}
                            id="userContracts"
                        >
                            {user.customer_contract_count}
                        </button>
                    </div>
                    <div className="flex justify-between my-[27px]">
                        <label className="text-[14px] font-[500]" htmlFor="guarantorContractCount">Kəfil olduğu müqavilə sayı</label>
                        <button
                            type="button"
                            onClick={user.guarantor_contract_count > 0 ? () => handleContractClick("guarantor") : null}
                            className={`w-1/2 text-start ${classes.userAddInput} ${user.guarantor_contract_count == 0 && "!cursor-default"}`}
                            id="guarantorContractCount"
                        >
                            {user.guarantor_contract_count}
                        </button>
                    </div>
                    <div className="flex justify-between my-[27px]">
                        <label className="text-[14px] font-[500]" htmlFor="attorneyContractCount">Vəkil olduğu müqavilə sayı</label>
                        <button
                            type="button"
                            onClick={user.attorney_contract_count > 0 ? () => handleContractClick("attorney") : null}
                            className={`w-1/2 text-start ${classes.userAddInput} ${user.attorney_contract_count == 0 && "!cursor-default"}`}
                            id="attorneyContractCount"
                        >
                            {user.attorney_contract_count}
                        </button>
                    </div>
                </>
            }
            {!id && (
                <>
                    <div className="flex justify-between my-[27px]">
                        <label className="text-[14px] font-[500]" htmlFor={id ? "new_balance" : "investor_balance"}>Balans (₼) *</label>
                        <input
                            {...register(id ? "new_balance" : "investor_balance", { valueAsNumber: true })}
                            type="number"
                            className={`w-1/2 ${classes.userAddInput}`}
                            defaultValue={id ? user.investor_balance?.balance : 0}
                            id={id ? "new_balance" : "investor_balance"}
                            disabled={user.investor_balance}
                            name={id ? "new_balance" : "investor_balance"}
                        />
                    </div>
                    {!id && errors.investor_balance && (
                        <p style={{ color: "red" }}>{errors.investor_balance.message}</p>
                    )}
                </>
            )}
            {id && canSeePortmanat
                && <div className="flex items-center justify-between my-[27px] w-full">
                    <label className="text-[14px] font-[500]" htmlFor="userBalance">Portmanatlar</label>
                    <FormControl
                        className="w-1/2"
                        variant="standard"
                    >
                        <Select
                            labelId="userBalance-label"
                            id="userBalance"
                            value=""
                            size="small"
                            displayEmpty
                        >
                            {user?.wallets?.map((wallet, index) => (
                                <MenuItem key={index} value={wallet.balance}>
                                    {wallet.name} - {wallet.balance} AZN
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </div>
            }
            <div className="flex justify-between my-[27px]">
                <label className="text-[14px] font-[500]" htmlFor="userDebt">Borc (₼)</label>
                <input
                    type="number"
                    {...register("customer_debt")}
                    className={`w-1/2 ${classes.userAddInput}`}
                    id="userDebt"
                    name="customer_debt"
                    disabled={id}
                    defaultValue={id ? user.customer_remaining_debt : ""}
                />
            </div>
            <TextField
                id="outlined-multiline-static"
                label="Qeyd"
                defaultValue={id ? user.note : ""}
                multiline
                {...register("note")}
                minRows={4}
                className="w-full"
                name="note"
            />
        </div>
    )
}

export default FinancialAndAdditionalForm
