import { useParams } from "react-router-dom"

import InstallmentTable from "@/components/Dashboard/Contracts/ContractDetail/Table/InstallmentTable";
import ContractDetailText from "@/components/Dashboard/Contracts/ContractDetail/Text/ContractDetailText";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";

import { useDispatch, useSelector } from "react-redux";
import { Helmet } from "react-helmet-async";
import { useEffect, useRef, useState } from "react";
import { setDynamicMainTitle } from "@/redux/features/layout/dynamicMainTitleSlice";
import { useGetContractInstallmentQuery, useGetSingleContractQuery, useUpdateContractInfoMutation } from "@/redux/services/contractApiSlice";
import PaymentModal from "@/components/shared/PaymentModal/PaymentModal";
import CancelContractBtn from "@/components/Dashboard/Contracts/ContractDetail/Buttons/CancelContractBtn";
import ActivateContractBtn from "@/components/Dashboard/Contracts/ContractDetail/Buttons/ActivateContractBtn";
import ExportContractBtn from "@/components/Dashboard/Contracts/ContractDetail/Buttons/ExportContractBtn";
import EditContractBtn from "@/components/Dashboard/Contracts/ContractDetail/Buttons/EditContractBtn";
import EditActions from "@/components/Dashboard/Contracts/ContractDetail/Buttons/EditActions";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";
import { clearData, clearSelectedValues, setData } from "@/redux/features/infiniteScrollSlice";

function ContractDetailPage() {
    const { id } = useParams();
    const dispatch = useDispatch();

    const [isEdit, setIsEdit] = useState(false);
    const formRef = useRef();

    const { data: contractDetail, isLoading: singleContractLoading, isError: isSingleContractError, error: singleContractError } = useGetSingleContractQuery(id);
    const { guarantor, executor, attorney, customer } = useSelector(state => state.infiniteScroll);
    const selectedCity = useSelector((state) => state.city.selectedCity);
    const [updateContractInfo, { isLoading }] = useUpdateContractInfoMutation();

    const params = {
        contract: id,
        limit: 24
    }

    useEffect(() => {
        dispatch(clearData());
        dispatch(setDynamicMainTitle(singleContractLoading ? "Yüklənilir..." : contractDetail?.unique_id));
        dispatch(setData({
            mainKey: "customer",
            key: "selectedValue",
            value: {
                fullname: contractDetail?.customer?.fullname,
                id: contractDetail?.customer?.id,
                phone1: contractDetail?.customer?.phone1,
            }
        }));
    }, [contractDetail?.customer, contractDetail?.unique_id, dispatch, id, singleContractLoading]);

    const { data: contractInstallment, isLoading: contractInstallmentLoading, isError: isInstallmentsError, error: installmentsError, refetch } = useGetContractInstallmentQuery(params);
    const { isModalOpen } = useSelector(state => state.contractPayment);

    if (isSingleContractError || isInstallmentsError) return (
        <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error:
            {
                singleContractError?.originalStatus
                || singleContractError?.status
                || installmentsError?.originalStatus
                || installmentsError?.status
            }
        </p>
    );

    const handleSubmit = async (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);

        const product_name = formData.get("product_name");
        const note = formData.get("note");

        const data = {
            id,
            updateStatusData: {
                product_name,
                note,
                city_id: selectedCity?.id || contractDetail.customer.city.id,
                guarantor_id: guarantor.selectedValue?.id || null,
                executor_id: executor.selectedValue?.id || null,
                attorney_id: attorney.selectedValue?.id || null,
                customer_id: customer.selectedValue?.id || null,
            }
        };

        try {
            await updateContractInfo(data).unwrap();
            toggleEditState();
            showToast("Müqavilə məlumatları uğurla yeniləndi", "success");
            dispatch(clearSelectedValues(["guarantor", "attorney", "executor"]));
        } catch (error) {
            const msg = errorFinder(error);
            showToast(msg, "error");
        }
    };

    function toggleEditState() { setIsEdit((prev) => !prev); }

    return (
        <div className="pt-7 pr-7 flex flex-col items-start justify-between w-full">
            {singleContractLoading || contractInstallmentLoading
                ? <>
                    <Helmet>
                        <title>inKredo | Müqavilə Yüklənilir...</title>
                    </Helmet>
                    <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
                        <ContentLoadingScreen />
                    </div>
                </>
                : <>
                    <Helmet>
                        <title>inKredo | Müqavilələr | {contractDetail.unique_id}</title>
                    </Helmet>
                    {
                        contractDetail
                            ? <ContractDetailText isEdit={isEdit} contractDetail={contractDetail} formRef={formRef} onSubmit={handleSubmit} />
                            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
                    }
                    {
                        contractInstallment
                            && contractInstallment.results
                            && contractInstallment.results.data
                            && contractInstallment.results.data.length > 0
                            ? <div className="overflow-auto w-[100%]">
                                <InstallmentTable
                                    contractDetail={contractDetail}
                                    contractInstallments={contractInstallment}
                                />
                                <PaymentModal
                                    isOpen={isModalOpen}
                                    contractInstallment={contractInstallment}
                                    refetch={refetch}
                                    params={params}
                                />
                            </div>
                            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
                    }
                    <div className="flex items-center gap-[12px] justify-end w-full">
                        {isEdit
                            ? <EditActions isEdit={isEdit} toggleEditState={toggleEditState} formRef={formRef} isLoading={isLoading} />
                            : <>
                                <ActivateContractBtn contractDetail={contractDetail} />
                                <CancelContractBtn contractDetail={contractDetail} />
                                <ExportContractBtn />
                                <EditContractBtn toggleEditState={toggleEditState} />
                            </>
                        }
                    </div>
                </>
            }
        </div>
    )
}

export default ContractDetailPage
