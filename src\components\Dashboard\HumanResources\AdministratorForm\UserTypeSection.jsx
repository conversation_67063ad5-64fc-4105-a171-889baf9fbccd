import UserAutocompleteWrapper from "@/components/shared/Autocomplete/UserAutocompleteWrapper";
import { setData } from "@/redux/features/infiniteScrollSlice";
import { FormControl, FormControlLabel, FormLabel, Radio, RadioGroup, TextField } from "@mui/material";
import { useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";

export const UserTypeSection = ({
    id,
    userType,
    setUserType,
    formKey,
    setFormKey,
    clearImage,
}) => {
    const dispatch = useDispatch();
    const { register, setValue, clearErrors, formState: { errors } } = useFormContext();

    const resetAll = () => {
        dispatch(setData({ mainKey: "administrator", key: "fullname", value: "" }));
        dispatch(setData({ mainKey: "administrator", key: "error", value: false }));
        dispatch(setData({ mainKey: "administrator", key: "selectedValue", value: null }));

        ["fullname", "phone1", "phone2", "email", "salary", "city", "profileImage"].forEach(f => setValue(f, ""));
        clearErrors();
        setFormKey(prev => prev + 1);
        clearImage();
    };

    const handleUserTypeChange = (e) => {
        const value = e.target.value;
        setUserType(value);
        dispatch(setData({ mainKey: "administrator", key: "selectedValue", value: null }));
        resetAll();
    };

    if (id) {
        return (
            <div className="space-y-4">
                <TextField
                    label="Ad Soyad Ata adı"
                    variant="standard"
                    {...register("fullname")}
                    error={!!errors.fullname}
                    helperText={errors.fullname?.message}
                    className="w-full mb-4"
                    required
                />
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <FormControl component="fieldset">
                <FormLabel component="legend" className="text-[14px] font-[500] text-black">
                    Ad Soyad Ata adı *
                </FormLabel>
                <RadioGroup row value={userType} onChange={handleUserTypeChange} className="mt-2">
                    <FormControlLabel value="existing" control={<Radio size="small" />} label="Mövcud" className="mr-6" />
                    <FormControlLabel value="new" control={<Radio size="small" />} label="Yeni" />
                </RadioGroup>
            </FormControl>
            {userType === "existing" ? (
                <div className="flex items-center justify-between">
                    <label className="text-[14px] font-[500]">İstifadəçi seçin *</label>
                    <div className="w-1/2">
                        <UserAutocompleteWrapper type="administrator" label="" isOptionalField={false} isLabelAvailable={false} />
                        {errors.fullname && <span className="text-red-500 text-sm">{errors.fullname.message}</span>}
                    </div>
                </div>
            ) : (
                <TextField
                    key={`fullname-new-${formKey}`}
                    label="Ad Soyad Ata adı"
                    variant="standard"
                    {...register("fullname")}
                    error={!!errors.fullname}
                    helperText={errors.fullname?.message}
                    className="w-full"
                    required
                />
            )}
        </div>
    );
};