import { clearSelectedValues } from "@/redux/features/infiniteScrollSlice";
import { useDispatch } from "react-redux";

function EditActions({ toggleEditState, formRef, isLoading }) {
    const dispatch = useDispatch();

    function handleCancelClick() {
        toggleEditState();
        dispatch(clearSelectedValues(["guarantor", "attorney", "executor"]));
    }

    return (
        <>
            <button
                disabled={isLoading}
                className="defButton bgGray mb-5"
                onClick={handleCancelClick}
            >
                Ləğv Et
            </button>
            <button
                disabled={isLoading}
                className="defButton bgGreen mb-5"
                onClick={() => formRef.current?.requestSubmit()}
            >

                {isLoading ? "Yaddaşa verilir..." : "Yaddaşa ver"}
            </button>
        </>
    )
}

export default EditActions