import { Dialog, DialogContent, TextField } from "@mui/material";
import { modalStyles } from "@/styles/shared/modalStyles";
import { useParams } from "react-router-dom";
import { useAddPortmanatMutation } from "@/redux/services/accountingApiSlice";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";

function PortmanatModal({ isOpen = false, modalHandler, refetch }) {
    const { id } = useParams();
    const [addPortmanat, { isLoading: addPortmanatLoading }] = useAddPortmanatMutation();

    async function handleSubmit(e) {
        e.preventDefault();

        const formData = Object.fromEntries(new FormData(e.target).entries());
        formData["balance"] = Number(formData["balance"]);
        formData["user"] = id;

        try {
            await addPortmanat(formData).unwrap();
            // dispatch(addPortmanatLocal(formData));
            modalHandler();
            refetch();
        } catch (error) {
            console.error(error);
            const msg = errorFinder(error);

            showToast(msg, "error");
        }
    }

    return (
        <Dialog
            sx={modalStyles.userForm.sxstyle}
            open={isOpen}
            onClose={() => modalHandler()}
            slotProps={modalStyles.userForm.slotprops}
            disableRestoreFocus
        >
            <DialogContent>
                <form onSubmit={handleSubmit} className="w-full h-full flex flex-col justify-between">
                    <h2 className={`modalTitle`}>Portmanat əlavə et</h2>
                    <div>
                        <div className="flex items-end w-full justify-between mb-[15px]">
                            <span className="w-1/3 text-[14px]">Adı</span>
                            <TextField className="w-2/3" variant="standard" name="name" required />
                        </div>
                        <div className="flex items-end w-full justify-between mb-[15px]">
                            <span className="w-1/3 text-[14px]">Məbləğ</span>
                            <TextField className="w-2/3" variant="standard" type="number" name="balance" required />
                        </div>
                    </div>
                    <div className="flex gap-[28px] justify-end mb-[3px]">
                        <button type="button" onClick={() => modalHandler()} className="defButton bgGray">Ləğv et</button>
                        <button
                            className="defButton bgGreen"
                            disabled={addPortmanatLoading}
                        >
                            Əlavə et
                        </button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    )
}

export default PortmanatModal
