import { Di<PERSON>, DialogContent, TextField } from "@mui/material";
import { modalStyles } from "@/styles/shared/modalStyles.js";
import { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { useGetPortmanatQuery, useTransferPortmanatMutation } from "@/redux/services/accountingApiSlice.js";
import UserAutocompleteWrapper from "@/components/shared/Autocomplete/UserAutocompleteWrapper.jsx";
import { errorFinder } from "@/util/errorHandler.js";
import PortmanatSelect from "./shared/PortmanatSelect";
import { showToast } from "@/util/showToast.js";

const initialPortmanat = {
  id: "",
  name: "",
  balance: ""
};

function PortmanatTransferModal({ isOpen = false, modalHandler }) {
  const { selectedValue } = useSelector(state => state.infiniteScroll["investor"]);

  const [portmanat, setPortmanat] = useState({
    source: initialPortmanat,
    target: initialPortmanat
  });
  const { source, target } = portmanat;

  const [amount, setAmount] = useState("");
  const [transferPortmanat, { isLoading }] = useTransferPortmanatMutation();
  const formRef = useRef(null);

  const queryObject = {
    user_id: selectedValue?.id || null
  };

  const { data: userPortmanats } = useGetPortmanatQuery(queryObject, { skip: !selectedValue?.id, refetchOnMountOrArgChange: true });

  const allPortmanats = userPortmanats?.results || [];

  useEffect(() => {
    if (!isOpen && formRef.current) {
      formRef.current.reset();
      setPortmanat({
        source: initialPortmanat,
        target: initialPortmanat
      });
      setAmount("");
    }
  }, [isOpen]);

  useEffect(() => {
    if (!selectedValue) {
      setPortmanat({
        source: initialPortmanat,
        target: initialPortmanat
      });
      setAmount("");
    }
  }, [selectedValue]);

  async function handleSubmit(e) {
    e.preventDefault();

    if (!source?.id || !target?.id || !amount || amount <= 0) {
      return;
    }

    try {
      const formData = new FormData(e.target);
      const transferData = {
        from_wallet: source.id,
        to_wallet: target.id,
        amount: Number(amount),
        note: formData.get('note') || null
      };

      await transferPortmanat(transferData).unwrap();
      modalHandler(null);

      showToast("Transfer uğurla başa çatdı", "success");
    } catch (error) {
      console.error(error);
      const msg = errorFinder(error);

      showToast(msg || "Transfer zamanı xəta baş verdi", "error");
    }
  }

  return (
    <Dialog
      open={isOpen}
      sx={modalStyles.portmanatTransfer.sxstyle}
      slotProps={modalStyles.portmanatTransfer.slotprops}
      onClose={() => modalHandler(null)}
    >
      <DialogContent>
        <form ref={formRef} onSubmit={handleSubmit} className="w-full">
          <h2 className="modalTitle mb-[30px]">Portmanat Transfer</h2>

          <UserAutocompleteWrapper
            type="investor"
            label="İnvestor"
            queryParams={{ is_investor: true }}
          />

          <div className="flex items-end w-full justify-between mb-[15px]">
            <span className="w-1/3 text-[14px]">Məxaric</span>
            <PortmanatSelect
              allPortmanats={allPortmanats}
              portmanat={portmanat}
              setPortmanat={setPortmanat}
              selectedValue={selectedValue}
              userPortmanats={userPortmanats}
              initialPortmanat={initialPortmanat}
              type={"source"}
            />
          </div>

          <div className="flex items-end w-full justify-between mb-[15px]">
            <span className="w-1/3 text-[14px]">Mədaxil</span>
            <PortmanatSelect
              allPortmanats={allPortmanats}
              portmanat={portmanat}
              setPortmanat={setPortmanat}
              selectedValue={selectedValue}
              userPortmanats={userPortmanats}
              initialPortmanat={initialPortmanat}
              type={"target"}
            />
          </div>

          <div className="flex items-end w-full justify-between mb-[35px]">
            <span className="w-1/3 text-[14px]">Məbləğ</span>
            <TextField
              className="w-2/3"
              variant="standard"
              name="amount"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              disabled={!source?.id || !target?.id}
            />
          </div>

          <div className="mt-[32px] mb-[45px]">
            <TextField className="w-full" label="Qeyd" multiline name="note" rows={4} />
          </div>

          <div className="flex gap-[28px] justify-end">
            <button
              type="button"
              onClick={() => modalHandler(null)}
              className="defButton bgGray"
              disabled={isLoading}
            >
              Ləğv et
            </button>
            <button
              type="submit"
              className="defButton bgGreen"
              disabled={!source?.id || !target?.id || !amount || amount <= 0 || isLoading}
            >
              {isLoading ? "Yüklənilir..." : "Transfer et"}
            </button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default PortmanatTransferModal
