import { Box, Modal, Skeleton } from "@mui/material"
import { useEffect, useState } from "react";

const slotProps = {
    backdrop: {
        sx: {
            backdropFilter: "blur(5px)",
            backgroundColor: "rgba(255,255,255,0.2)",
        },
    },
}

function ImagePreviewModal({ open, handleClose, modalContent }) {
    const [imageLoaded, setImageLoaded] = useState(false);

    useEffect(() => {
        if (open) setImageLoaded(false);
    }, [open]);

    return (
        <Modal
            open={open}
            onClose={handleClose}
            slotProps={slotProps}
        >
            <Box sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                bgcolor: 'background.paper',
                boxShadow: 24,
                p: 2,
                minWidth: "100px",
                minHeight: "100px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                maxWidth: '80%',
                maxHeight: '80%',
                overflow: 'auto',
                padding: "10px !important",
                outline: 0
            }}>
                {modalContent && (
                    <>
                        {!imageLoaded && (
                            <Skeleton
                                variant="rectangular"
                                width={300}
                                height={300}
                                animation="wave"
                                sx={{ maxWidth: '100%', height: 'auto' }}
                            />
                        )}
                        <img
                            src={modalContent}
                            alt="receipt"
                            onLoad={() => setImageLoaded(true)}
                            onError={() => setImageLoaded(true)}
                            style={{
                                display: imageLoaded ? "block" : "none",
                                maxWidth: '100%',
                                height: 'auto',
                                objectFit: "contain"
                            }}
                        />
                    </>
                )}
            </Box>
        </Modal>
    )
}

export default ImagePreviewModal